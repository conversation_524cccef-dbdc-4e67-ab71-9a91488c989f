// Search performance optimization utilities

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  hits: number;
}

class PerformanceCache<T> {
  private cache = new Map<string, CacheEntry<T>>();
  private readonly maxSize: number;
  private readonly ttl: number; // Time to live in milliseconds

  constructor(maxSize: number = 100, ttlMinutes: number = 10) {
    this.maxSize = maxSize;
    this.ttl = ttlMinutes * 60 * 1000;
  }

  set(key: string, data: T): void {
    // Evict oldest entries if cache is full
    if (this.cache.size >= this.maxSize) {
      const oldestKey = this.cache.keys().next().value;
      if (oldestKey) {
        this.cache.delete(oldestKey);
      }
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      hits: 0
    });
  }

  get(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    // Check if entry has expired
    if (Date.now() - entry.timestamp > this.ttl) {
      this.cache.delete(key);
      return null;
    }

    // Increment hit counter
    entry.hits++;
    return entry.data;
  }

  has(key: string): boolean {
    return this.get(key) !== null;
  }

  clear(): void {
    this.cache.clear();
  }

  getStats(): { size: number; totalHits: number; hitRate: number } {
    const entries = Array.from(this.cache.values());
    const totalHits = entries.reduce((sum, entry) => sum + entry.hits, 0);
    const totalRequests = entries.length + totalHits;
    
    return {
      size: this.cache.size,
      totalHits,
      hitRate: totalRequests > 0 ? (totalHits / totalRequests) * 100 : 0
    };
  }
}

// Global caches for different types of data
export const documentCountCache = new PerformanceCache<number>(50, 5); // 5 minute TTL
export const searchResultsCache = new PerformanceCache<any>(200, 10); // 10 minute TTL
export const embeddingCache = new PerformanceCache<number[]>(500, 30); // 30 minute TTL

// Performance monitoring utilities
export class SearchPerformanceOptimizer {
  private static performanceLog: Array<{
    operation: string;
    duration: number;
    timestamp: number;
    metadata?: any;
  }> = [];

  static logOperation(operation: string, duration: number, metadata?: any): void {
    this.performanceLog.push({
      operation,
      duration,
      timestamp: Date.now(),
      metadata
    });

    // Keep only last 100 operations
    if (this.performanceLog.length > 100) {
      this.performanceLog = this.performanceLog.slice(-100);
    }

    // Log slow operations
    if (duration > 1000) {
      console.warn(`⚠️ Slow operation detected: ${operation} took ${duration}ms`, metadata);
    }
  }

  static async measureAsync<T>(
    operation: string,
    fn: () => Promise<T>,
    metadata?: any
  ): Promise<T> {
    const start = Date.now();
    try {
      const result = await fn();
      const duration = Date.now() - start;
      this.logOperation(operation, duration, metadata);
      return result;
    } catch (error) {
      const duration = Date.now() - start;
      this.logOperation(`${operation} (failed)`, duration, { ...metadata, error: String(error) });
      throw error;
    }
  }

  static measure<T>(
    operation: string,
    fn: () => T,
    metadata?: any
  ): T {
    const start = Date.now();
    try {
      const result = fn();
      const duration = Date.now() - start;
      this.logOperation(operation, duration, metadata);
      return result;
    } catch (error) {
      const duration = Date.now() - start;
      this.logOperation(`${operation} (failed)`, duration, { ...metadata, error: String(error) });
      throw error;
    }
  }

  static getPerformanceStats(): {
    averageDuration: number;
    slowOperations: number;
    totalOperations: number;
    recentOperations: Array<{ operation: string; duration: number; timestamp: number }>;
  } {
    const totalDuration = this.performanceLog.reduce((sum, log) => sum + log.duration, 0);
    const slowOperations = this.performanceLog.filter(log => log.duration > 1000).length;

    return {
      averageDuration: this.performanceLog.length > 0 ? totalDuration / this.performanceLog.length : 0,
      slowOperations,
      totalOperations: this.performanceLog.length,
      recentOperations: this.performanceLog.slice(-10).map(log => ({
        operation: log.operation,
        duration: log.duration,
        timestamp: log.timestamp
      }))
    };
  }

  static clearStats(): void {
    this.performanceLog = [];
  }
}

// Debounce utility for search optimization
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Throttle utility for rate limiting
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// Search query optimization
export class SearchQueryOptimizer {
  static optimizeQuery(query: string): string {
    return query
      .trim()
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ') // Remove special characters
      .replace(/\s+/g, ' ') // Normalize whitespace
      .substring(0, 200); // Limit length
  }

  static shouldUseCache(query: string): boolean {
    const optimized = this.optimizeQuery(query);
    return optimized.length >= 2 && optimized.length <= 100;
  }

  static getCacheKey(query: string, options?: any): string {
    const optimized = this.optimizeQuery(query);
    const optionsStr = options ? JSON.stringify(options) : '';
    return `search:${optimized}:${optionsStr}`;
  }
}

// Export performance monitoring hook for React components
export function useSearchOptimization() {
  const measureOperation = <T>(operation: string, fn: () => T, metadata?: any): T => {
    return SearchPerformanceOptimizer.measure(operation, fn, metadata);
  };

  const measureAsyncOperation = async <T>(
    operation: string,
    fn: () => Promise<T>,
    metadata?: any
  ): Promise<T> => {
    return SearchPerformanceOptimizer.measureAsync(operation, fn, metadata);
  };

  const getStats = () => SearchPerformanceOptimizer.getPerformanceStats();
  const clearStats = () => SearchPerformanceOptimizer.clearStats();

  return {
    measureOperation,
    measureAsyncOperation,
    getStats,
    clearStats,
    debounce,
    throttle
  };
}
