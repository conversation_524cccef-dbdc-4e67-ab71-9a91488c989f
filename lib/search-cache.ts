// Search result caching utility for smooth transitions

interface CachedSearchResult {
  data: any;
  timestamp: number;
  uuid?: string;
}

class SearchCache {
  private cache = new Map<string, CachedSearchResult>();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  // Generate cache key from query
  private getCacheKey(query: string): string {
    return `search:${query.toLowerCase().trim()}`;
  }

  // Check if cached result is still valid
  private isValid(cached: CachedSearchResult): boolean {
    return Date.now() - cached.timestamp < this.CACHE_DURATION;
  }

  // Store search result in cache
  set(query: string, data: any, uuid?: string): void {
    const key = this.getCacheKey(query);
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      uuid
    });
  }

  // Retrieve cached search result
  get(query: string): CachedSearchResult | null {
    const key = this.getCacheKey(query);
    const cached = this.cache.get(key);
    
    if (!cached || !this.isValid(cached)) {
      this.cache.delete(key);
      return null;
    }
    
    return cached;
  }

  // Check if query has cached results
  has(query: string): boolean {
    return this.get(query) !== null;
  }

  // Clear expired entries
  cleanup(): void {
    for (const [key, cached] of this.cache.entries()) {
      if (!this.isValid(cached)) {
        this.cache.delete(key);
      }
    }
  }

  // Clear all cache
  clear(): void {
    this.cache.clear();
  }

  // Get cache size
  size(): number {
    return this.cache.size;
  }
}

// Global cache instance
export const searchCache = new SearchCache();

// Auto cleanup every 5 minutes
if (typeof window !== 'undefined') {
  setInterval(() => {
    searchCache.cleanup();
  }, 5 * 60 * 1000);
}

// Search state management for transitions
interface SearchState {
  query: string;
  isLoading: boolean;
  results: any | null;
  uuid: string | null;
  error: string | null;
}

class SearchStateManager {
  private state: SearchState = {
    query: '',
    isLoading: false,
    results: null,
    uuid: null,
    error: null
  };

  private listeners = new Set<(state: SearchState) => void>();

  // Subscribe to state changes
  subscribe(listener: (state: SearchState) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  // Notify all listeners
  private notify(): void {
    this.listeners.forEach(listener => listener(this.state));
  }

  // Update state
  setState(updates: Partial<SearchState>): void {
    this.state = { ...this.state, ...updates };
    this.notify();
  }

  // Get current state
  getState(): SearchState {
    return { ...this.state };
  }

  // Reset state
  reset(): void {
    this.state = {
      query: '',
      isLoading: false,
      results: null,
      uuid: null,
      error: null
    };
    this.notify();
  }
}

export const searchStateManager = new SearchStateManager();
