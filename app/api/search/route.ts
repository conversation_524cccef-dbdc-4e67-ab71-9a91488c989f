import { NextRequest, NextResponse } from 'next/server';
import { OpenRouterClient } from '@/lib/openrouter';
import { SupabaseVectorStore } from '@/lib/supabase-vector-store';
import { SecurityUtils, SECURITY_CONFIG } from '@/lib/security-config';
import { saveSearchQuery } from '@/lib/search-history';
import { supabase } from '@/lib/supabase';
import { v4 as uuidv4 } from 'uuid';
import {
  SearchPerformanceOptimizer,
  SearchQueryOptimizer,
  documentCountCache,
  searchResultsCache
} from '@/lib/search-performance-optimizer';

export const dynamic = 'force-dynamic';

// Get client IP for logging
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const cfConnectingIP = request.headers.get('cf-connecting-ip');

  if (cfConnectingIP) return cfConnectingIP;
  if (realIP) return realIP;
  if (forwarded) return forwarded.split(',')[0].trim();

  return 'unknown';
}

export async function POST(request: NextRequest) {
  const apiStartTime = Date.now();
  const clientIP = getClientIP(request);

  // OPTIMIZATION: Add overall request timeout to prevent hanging
  const requestTimeout = 30000; // 30 second timeout

  const mainLogic = async () => {
    const { query } = await request.json();

    // Input validation and sanitization
    if (!query || typeof query !== 'string') {
      SecurityUtils.logSecurityEvent({
        type: 'suspicious_activity',
        ip: clientIP,
        endpoint: '/api/search',
        details: { reason: 'Invalid query format', query: typeof query }
      });

      return NextResponse.json(
        { error: 'Invalid query format' },
        { status: 400 }
      );
    }

    // OPTIMIZATION: Sanitize and optimize query
    const sanitizedQuery = SearchQueryOptimizer.optimizeQuery(SecurityUtils.sanitizeInput(query));

    if (sanitizedQuery.length === 0) {
      return NextResponse.json(
        { error: 'Query cannot be empty' },
        { status: 400 }
      );
    }

    if (sanitizedQuery.length > SECURITY_CONFIG.CONTENT.MAX_QUERY_LENGTH) {
      return NextResponse.json(
        { error: `Query too long. Maximum ${SECURITY_CONFIG.CONTENT.MAX_QUERY_LENGTH} characters allowed.` },
        { status: 400 }
      );
    }

    console.log(`🔍 Processing search query: "${sanitizedQuery}"`);

    // OPTIMIZATION: Check cache first
    const cacheKey = SearchQueryOptimizer.getCacheKey(sanitizedQuery);
    const cachedResult = searchResultsCache.get(cacheKey);
    if (cachedResult) {
      console.log(`⚡ Cache hit for query: "${sanitizedQuery}"`);
      return NextResponse.json({
        ...cachedResult,
        cached: true,
        timestamp: new Date().toISOString()
      });
    }

    // Save search query to history (async, don't wait for completion)
    saveSearchQuery(sanitizedQuery).catch(error => {
      console.error('Error saving search query to history:', error);
    });

    // Initialize Supabase vector store with admin access (no user filtering)
    const vectorStore = new SupabaseVectorStore();

    // OPTIMIZATION: Check document count with caching
    const docCountStart = Date.now();
    const countCacheKey = 'document_count';
    let documentCount = documentCountCache.get(countCacheKey);

    if (documentCount === null) {
      documentCount = await vectorStore.getDocumentCount();
      documentCountCache.set(countCacheKey, documentCount);
    }

    const docCountTime = Date.now() - docCountStart;
    console.log(`📊 Document count check: ${docCountTime}ms (${documentCount} documents)`);

    if (documentCount === 0) {
      const emptyResponseData = {
        results: [],
        totalResults: 0,
        query: sanitizedQuery,
        message: "No knowledge base sources have been added yet. The knowledge base is currently empty.",
        suggestions: [],
        timestamp: new Date().toISOString()
      };

      // Store empty search results for sharing
      const resultUuid = uuidv4();
      supabase
        .from('search_results')
        .insert({
          id: resultUuid,
          query: sanitizedQuery,
          results: emptyResponseData,
          user_id: null,
          expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
        })
        .then(({ error: insertError }) => {
          if (insertError) {
            console.error('Error storing empty search results:', insertError);
          }
        });

      return NextResponse.json({
        ...emptyResponseData,
        uuid: resultUuid
      });
    }

    // OPTIMIZATION: Use optimized search with timeout
    const searchStart = Date.now();
    const searchTimeout = 15000; // 15 second timeout
    const searchPromise = vectorStore.searchWithKeywords(sanitizedQuery, 10, 0.02);

    const searchResults = await Promise.race([
      searchPromise,
      new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Search timeout')), searchTimeout)
      )
    ]);

    const searchTime = Date.now() - searchStart;
    console.log(`🔍 Vector search time: ${searchTime}ms (${searchResults.length} results)`);
    
    if (searchResults.length === 0) {
      // Get available information for user guidance
      const stats = await vectorStore.getStats();

      console.log(`❌ No search results found for: "${sanitizedQuery}"`);
      console.log(`📋 Available topics: ${stats.topics.slice(0, 10).join(', ')}`);
      console.log(`📄 Available sources: ${stats.sources.join(', ')}`);

      const noResultsData = {
        results: [],
        totalResults: 0,
        query: sanitizedQuery,
        message: `No results found for "${sanitizedQuery}". Try searching for one of these topics instead.`,
        suggestions: stats.topics.slice(0, 8),
        availableSources: stats.sources.slice(0, 5),
        timestamp: new Date().toISOString()
      };

      // Store no-results search for sharing
      const resultUuid = uuidv4();
      supabase
        .from('search_results')
        .insert({
          id: resultUuid,
          query: sanitizedQuery,
          results: noResultsData,
          user_id: null,
          expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
        })
        .then(({ error: insertError }) => {
          if (insertError) {
            console.error('Error storing no-results search:', insertError);
          }
        });

      return NextResponse.json({
        ...noResultsData,
        uuid: resultUuid
      });
    }

    // Format search results for structured display
    const formattedResults = searchResults.map((result, index) => ({
      id: `result-${index}`,
      title: result.document.metadata.title,
      content: result.document.content,
      score: result.score,
      matchType: result.matchType,
      source: result.document.metadata.source,
      sourceUrl: result.document.metadata.sourceUrl,
      type: result.document.metadata.type,
      topics: result.document.metadata.topics,
      addedAt: result.document.metadata.addedAt,
      relevanceScore: Math.round(result.score * 100)
    }));

    // Collect unique sources and source URLs
    const sources = [...new Set(searchResults.map(result => result.document.metadata.title))];
    const sourceUrls: { [title: string]: string } = {};
    
    searchResults.forEach(result => {
      const title = result.document.metadata.title;
      const url = result.document.metadata.sourceUrl;
      if (url) {
        sourceUrls[title] = url;
      }
    });

    console.log(`✅ Found ${searchResults.length} relevant documents`);
    console.log(`📄 Sources: ${sources.join(', ')}`);

    // OPTIMIZATION: Generate AI summary with timeout
    let aiSummary = null;
    const openRouterApiKey = process.env.OPENROUTER_API_KEY;

    if (openRouterApiKey && searchResults.length > 0) {
      try {
        const aiStart = Date.now();
        const context = searchResults.slice(0, 3).map(result =>
          `Source: ${result.document.metadata.title}\nContent: ${result.document.content}`
        ).join('\n\n');

        const openRouter = new OpenRouterClient(openRouterApiKey);

        // Add 10 second timeout for AI summary generation
        const aiTimeout = 10000;
        const aiPromise = openRouter.generateResponse(sanitizedQuery, context);

        aiSummary = await Promise.race([
          aiPromise,
          new Promise<null>((resolve) =>
            setTimeout(() => {
              console.warn('⚠️ AI summary generation timed out');
              resolve(null);
            }, aiTimeout)
          )
        ]);

        const aiTime = Date.now() - aiStart;
        console.log(`🤖 AI summary generation time: ${aiTime}ms`);
      } catch (aiError) {
        console.error('OpenRouter API error:', aiError);
        // Continue without AI summary
      }
    }

    const embeddingType = await vectorStore.getStats().then(stats => stats.embeddingType);

    // Prepare the response data
    const responseData = {
      results: formattedResults,
      totalResults: searchResults.length,
      query: sanitizedQuery,
      aiSummary,
      sources: sources,
      sourceUrls: Object.keys(sourceUrls).length > 0 ? sourceUrls : undefined,
      searchScores: searchResults.map(r => (r.score * 100).toFixed(1)),
      embeddingType,
      timestamp: new Date().toISOString()
    };

    // Check if sharing is requested via query parameter
    const url = new URL(request.url);
    const enableSharing = url.searchParams.get('share') === 'true';

    if (enableSharing) {
      // Only store in database when sharing is explicitly requested
      const resultUuid = uuidv4();
      supabase
        .from('search_results')
        .insert({
          id: resultUuid,
          query: sanitizedQuery,
          results: responseData,
          user_id: null,
          expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
        })
        .then(({ error: insertError }) => {
          if (insertError) {
            console.error('Error storing search results for sharing:', insertError);
          } else {
            console.log(`✅ Search results stored for sharing with UUID: ${resultUuid}`);
          }
        });

      return NextResponse.json({
        ...responseData,
        uuid: resultUuid,
        isShared: true
      });
    }

    // Log total API processing time with performance analysis
    const totalApiTime = Date.now() - apiStartTime;
    console.log(`⏱️  Total API processing time: ${totalApiTime}ms`);

    // OPTIMIZATION: Log performance warnings for slow searches
    if (totalApiTime > 5000) {
      console.error('🚨 Very slow search detected (>5s)');
      console.error('   Consider investigating the bottlenecks identified above');
    } else if (totalApiTime > 2000) {
      console.warn('⚠️  Slow search detected (>2s)');
    } else if (totalApiTime < 1000) {
      console.log(`✅ Fast search completed in ${totalApiTime}ms`);
    }

    // OPTIMIZATION: Cache the results for future requests
    searchResultsCache.set(cacheKey, {
      ...responseData,
      isShared: false
    });

    // Return immediate response without database storage for faster performance
    return NextResponse.json({
      ...responseData,
      isShared: false
    });
  };

  try {
    // OPTIMIZATION: Race between main logic and timeout
    const timeoutPromise = new Promise<never>((_, reject) =>
      setTimeout(() => reject(new Error('Request timeout')), requestTimeout)
    );

    return await Promise.race([mainLogic(), timeoutPromise]);

  } catch (error) {
    console.error('Search API error:', error);

    if (error instanceof Error && error.message === 'Request timeout') {
      return NextResponse.json(
        { error: 'Search request timed out. Please try again.' },
        { status: 408 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const query = searchParams.get('q');

  if (!query) {
    return NextResponse.json(
      { error: 'Query parameter "q" is required' },
      { status: 400 }
    );
  }

  // Convert GET request to POST format
  return POST(new NextRequest(request.url, {
    method: 'POST',
    headers: request.headers,
    body: JSON.stringify({ query })
  }));
}
