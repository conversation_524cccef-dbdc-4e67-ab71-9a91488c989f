'use client';

import { useState, useEffect } from 'react';
import { Progress } from '@/components/ui/progress';
import { Brain, Search, Sparkles, CheckCircle } from 'lucide-react';

export type SearchPhase = 'searching' | 'analyzing' | 'generating' | 'complete';

interface SearchProgressProps {
  isVisible: boolean;
  currentPhase: SearchPhase;
  isApiComplete: boolean;
  onComplete?: () => void;
}

const searchSteps = [
  { id: 'searching', label: 'Searching knowledge base...', icon: Search },
  { id: 'analyzing', label: 'Analyzing results...', icon: Brain },
  { id: 'generating', label: 'Generating AI summary...', icon: Sparkles },
  { id: 'complete', label: 'Search complete!', icon: CheckCircle }
];

export default function SearchProgress({
  isVisible,
  currentPhase,
  isApiComplete,
  onComplete
}: SearchProgressProps) {
  const [progress, setProgress] = useState(0);
  const [currentStepIndex, setCurrentStepIndex] = useState(0);

  // Find current step index based on phase
  const getStepIndex = (phase: SearchPhase): number => {
    return searchSteps.findIndex(step => step.id === phase);
  };

  useEffect(() => {
    if (!isVisible) {
      setProgress(0);
      setCurrentStepIndex(0);
      return;
    }

    const stepIndex = getStepIndex(currentPhase);
    setCurrentStepIndex(stepIndex);

    // Calculate progress based on current phase and API completion status
    let targetProgress: number;

    if (isApiComplete) {
      // API is complete, show 100% progress
      targetProgress = 100;
    } else {
      // API still processing, show progress based on current phase
      // Each phase represents 25% of total progress, but don't complete until API is done
      const baseProgress = (stepIndex / searchSteps.length) * 100;
      const phaseProgress = (1 / searchSteps.length) * 100;

      // Show partial progress within current phase (up to 80% of phase completion)
      // This gives visual feedback while preventing completion before API finishes
      targetProgress = Math.min(baseProgress + (phaseProgress * 0.8), 95);
    }

    // Animate progress smoothly to target
    const animateProgress = () => {
      setProgress(current => {
        const diff = targetProgress - current;
        if (Math.abs(diff) < 0.1) {
          return targetProgress;
        }
        // Smooth animation - faster when far from target, slower when close
        return current + (diff * 0.1);
      });
    };

    const progressInterval = setInterval(animateProgress, 50);

    return () => {
      clearInterval(progressInterval);
    };
  }, [isVisible, currentPhase, isApiComplete]);

  // Trigger completion when API is done and progress reaches 100%
  useEffect(() => {
    if (isApiComplete && progress >= 99.9) {
      const completeTimer = setTimeout(() => {
        onComplete?.();
      }, 500); // Small delay to show completion state

      return () => clearTimeout(completeTimer);
    }
  }, [isApiComplete, progress, onComplete]);

  if (!isVisible) return null;

  const currentStepData = searchSteps[currentStepIndex];
  const IconComponent = currentStepData?.icon || Search;

  return (
    <div className="fixed inset-0 bg-white/90 backdrop-blur-sm z-50 flex items-center justify-center">
      <div className="bg-white rounded-lg shadow-xl p-8 max-w-md w-full mx-4 border border-slate-200">
        <div className="text-center">
          {/* Animated Icon */}
          <div className="w-16 h-16 mx-auto mb-6 relative">
            <div className="absolute inset-0 bg-[#FF6800] rounded-full opacity-20 animate-ping" />
            <div className="relative w-16 h-16 bg-[#FF6800] rounded-full flex items-center justify-center">
              <IconComponent className="w-8 h-8 text-white" />
            </div>
          </div>

          {/* Step Label */}
          <h3 className="text-lg font-semibold text-slate-800 mb-2">
            {currentStepData?.label || 'Processing...'}
          </h3>

          {/* Progress Bar */}
          <div className="mb-4">
            <Progress
              value={progress}
              className="h-2 bg-slate-100"
            />
            <p className="text-sm text-slate-500 mt-2">
              {Math.round(progress)}% complete
            </p>
          </div>

          {/* Step Indicators */}
          <div className="flex justify-center space-x-2">
            {searchSteps.map((step, index) => (
              <div
                key={step.id}
                className={`w-2 h-2 rounded-full transition-colors duration-300 ${
                  index <= currentStepIndex
                    ? 'bg-[#FF6800]'
                    : 'bg-slate-200'
                }`}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
